package dao

import (
	"gitee.com/rcztcs/zjl/internal/zjl/model"
	"gorm.io/gorm"
	"time"
)

// TaskDao 任务数据访问层接口
type TaskDao interface {
	// Create 创建任务
	Create(task *model.Task) error

	// GetByID 根据ID获取任务
	GetByID(id uint) (*model.Task, error)

	// GetByReportID 根据报告ID获取任务
	GetByReportID(reportID string) (*model.Task, error)

	// Update 更新任务信息
	Update(id uint, status model.TaskStatus) error

	// List 分页查询任务列表（支持多条件筛选）
	List(page, pageSize int, userID uint, status model.TaskStatus, startTime, endTime *time.Time) ([]*model.Task, int64, error)
}

// taskDao 任务数据访问层实现
type taskDao struct {
	db *gorm.DB
}

// NewTaskDao 创建任务数据访问层实例
func NewTaskDao(db *gorm.DB) TaskDao {
	return &taskDao{
		db: db,
	}
}

// Create 创建任务
func (d *taskDao) Create(task *model.Task) error {
	return d.db.Create(task).Error
}

// GetByID 根据ID获取任务
func (d *taskDao) GetByID(id uint) (*model.Task, error) {
	var task model.Task
	err := d.db.Where("id = ?", id).First(&task).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// GetByReportID 根据报告ID获取任务
func (d *taskDao) GetByReportID(reportID string) (*model.Task, error) {
	var task model.Task
	err := d.db.Where("report_id = ?", reportID).First(&task).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// Update 更新任务信息
func (d *taskDao) Update(id uint, status model.TaskStatus) error {

	err := d.db.Model(&model.Task{}).Where("id = ?", id).Updates(map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}).Error
	if err != nil {
		return err
	}
	return nil
}

// List 分页查询任务列表（支持多条件筛选）
func (d *taskDao) List(page, pageSize int, userID uint, status model.TaskStatus, startTime, endTime *time.Time) ([]*model.Task, int64, error) {
	var tasks []*model.Task
	var total int64

	// 构建查询条件
	query := d.db.Model(&model.Task{})

	if userID != 0 {
		query = query.Where("user_id = ?", userID)
	}

	if status != "" {
		query = query.Where("status = ?", status)
	}

	if startTime != nil && endTime != nil {
		query = query.Where("created_at >= ? AND created_at <= ?", *startTime, *endTime)
	}

	// 统计总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = query.Offset(offset).Limit(pageSize).
		Order("created_at DESC").
		Find(&tasks).Error

	return tasks, total, err
}
