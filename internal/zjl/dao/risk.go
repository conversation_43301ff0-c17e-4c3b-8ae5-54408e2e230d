package dao

import (
	"gitee.com/rcztcs/zjl/internal/zjl/model"
	"gorm.io/gorm"
)

// RiskDao 风险数据访问层接口
type RiskDao interface {
	// Create 创建风险
	Create(risk *model.Risk) error

	// GetByID 根据ID获取风险
	GetByID(id uint) (*model.Risk, error)

	// GetByTaskID 根据任务ID获取风险列表
	GetByTaskID(taskID uint) ([]*model.Risk, error)

	// List 分页查询风险列表（支持多条件筛选）
	List(page, pageSize int, taskID uint, riskLevel model.RiskLevel, indicatorType string) ([]*model.Risk, int64, error)

	// Update 更新风险信息
	Update(risk *model.Risk) error

	// Delete 删除风险
	Delete(id uint) error

	// Count 统计风险数量
	Count(taskID uint, riskLevel model.RiskLevel, indicatorType string) (int64, error)
}

// riskDao 风险数据访问层实现
type riskDao struct {
	db *gorm.DB
}

// NewRiskDao 创建风险数据访问层实例
func NewRiskDao(db *gorm.DB) RiskDao {
	return &riskDao{
		db: db,
	}
}

// Create 创建风险
func (d *riskDao) Create(risk *model.Risk) error {
	return d.db.Create(risk).Error
}

// GetByID 根据ID获取风险
func (d *riskDao) GetByID(id uint) (*model.Risk, error) {
	var risk model.Risk
	err := d.db.Where("id = ?", id).First(&risk).Error
	if err != nil {
		return nil, err
	}
	return &risk, nil
}

// GetByTaskID 根据任务ID获取风险列表
func (d *riskDao) GetByTaskID(taskID uint) ([]*model.Risk, error) {
	var risks []*model.Risk
	err := d.db.Where("task_id = ?", taskID).Find(&risks).Error
	return risks, err
}

// List 分页查询风险列表（支持多条件筛选）
func (d *riskDao) List(page, pageSize int, taskID uint, riskLevel model.RiskLevel, indicatorType string) ([]*model.Risk, int64, error) {
	var risks []*model.Risk
	var total int64

	query := d.db.Model(&model.Risk{})

	// 条件筛选
	if taskID > 0 {
		query = query.Where("task_id = ?", taskID)
	}
	if riskLevel != "" {
		query = query.Where("risk_level = ?", riskLevel)
	}
	if indicatorType != "" {
		query = query.Where("indicator_type LIKE ?", "%"+indicatorType+"%")
	}

	// 统计总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&risks).Error
	if err != nil {
		return nil, 0, err
	}

	return risks, total, nil
}

// Update 更新风险信息
func (d *riskDao) Update(risk *model.Risk) error {
	return d.db.Save(risk).Error
}

// Delete 删除风险
func (d *riskDao) Delete(id uint) error {
	return d.db.Delete(&model.Risk{}, id).Error
}

// Count 统计风险数量
func (d *riskDao) Count(taskID uint, riskLevel model.RiskLevel, indicatorType string) (int64, error) {
	var count int64
	query := d.db.Model(&model.Risk{})

	// 条件筛选
	if taskID > 0 {
		query = query.Where("task_id = ?", taskID)
	}
	if riskLevel != "" {
		query = query.Where("risk_level = ?", riskLevel)
	}
	if indicatorType != "" {
		query = query.Where("indicator_type LIKE ?", "%"+indicatorType+"%")
	}

	err := query.Count(&count).Error
	return count, err
}
