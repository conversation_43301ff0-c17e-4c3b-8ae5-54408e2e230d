package service

import (
	"errors"
	"gitee.com/rcztcs/zjl/internal/pkg/utils"
	"gitee.com/rcztcs/zjl/internal/zjl/dao"
	"gitee.com/rcztcs/zjl/internal/zjl/model"
	"gorm.io/gorm"
)

// RiskService 风险服务层接口
type RiskService interface {
	// CreateRisk 创建风险
	CreateRisk(taskID uint, riskLevel model.RiskLevel, indicatorType, indicatorName string) (*model.Risk, error)

	// GetRiskByID 根据ID获取风险详情
	GetRiskByID(id uint) (*model.Risk, error)

	// GetRisksByTaskID 根据任务ID获取风险列表
	GetRisksByTaskID(taskID uint) ([]*model.Risk, error)

	// ListRisks 分页查询风险列表（支持多条件筛选）
	ListRisks(page, pageSize int, taskID uint, riskLevel model.RiskLevel, indicatorType string) ([]*model.Risk, int64, error)

	// UpdateRisk 更新风险信息
	UpdateRisk(id uint, riskLevel model.RiskLevel, indicatorType, indicatorName string) error

	// DeleteRisk 删除风险
	DeleteRisk(id uint) error
}

// riskService 风险服务层实现
type riskService struct {
	riskDao dao.RiskDao
	taskDao dao.TaskDao
}

// NewRiskService 创建风险服务层实例
func NewRiskService(riskDao dao.RiskDao, taskDao dao.TaskDao) RiskService {
	return &riskService{
		riskDao: riskDao,
		taskDao: taskDao,
	}
}

// CreateRisk 创建风险
func (s *riskService) CreateRisk(taskID uint, riskLevel model.RiskLevel, indicatorType, indicatorName string) (*model.Risk, error) {
	// 参数校验
	if taskID == 0 {
		return nil, utils.NewAppError(utils.ErrInvalidParamsCode, "任务ID不能为空")
	}
	if riskLevel == "" {
		return nil, utils.NewAppError(utils.ErrInvalidParamsCode, "风险类别不能为空")
	}
	if indicatorType == "" {
		return nil, utils.NewAppError(utils.ErrInvalidParamsCode, "指标大类不能为空")
	}
	if indicatorName == "" {
		return nil, utils.NewAppError(utils.ErrInvalidParamsCode, "指标名称不能为空")
	}

	// 验证风险类别是否有效
	if riskLevel != model.RiskLevelHigh && riskLevel != model.RiskLevelMedium && riskLevel != model.RiskLevelLow {
		return nil, utils.NewAppError(utils.ErrInvalidParamsCode, "无效的风险类别")
	}

	// 验证任务是否存在
	_, err := s.taskDao.GetByID(taskID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, utils.NewAppError(utils.ErrNotFoundCode, "任务不存在")
		}
		return nil, err
	}

	// 创建风险
	risk := &model.Risk{
		TaskID:        taskID,
		RiskLevel:     riskLevel,
		IndicatorType: indicatorType,
		IndicatorName: indicatorName,
	}

	err = s.riskDao.Create(risk)
	if err != nil {
		return nil, utils.NewAppError(utils.ErrInternalCode, "创建风险失败")
	}

	return risk, nil
}

// GetRiskByID 根据ID获取风险详情
func (s *riskService) GetRiskByID(id uint) (*model.Risk, error) {
	if id == 0 {
		return nil, utils.NewAppError(utils.ErrInvalidParamsCode, "风险ID不能为空")
	}

	risk, err := s.riskDao.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, utils.NewAppError(utils.ErrNotFoundCode, "风险不存在")
		}
		return nil, err
	}

	return risk, nil
}

// GetRisksByTaskID 根据任务ID获取风险列表
func (s *riskService) GetRisksByTaskID(taskID uint) ([]*model.Risk, error) {
	if taskID == 0 {
		return nil, utils.NewAppError(utils.ErrInvalidParamsCode, "任务ID不能为空")
	}

	// 验证任务是否存在
	_, err := s.taskDao.GetByID(taskID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, utils.NewAppError(utils.ErrNotFoundCode, "任务不存在")
		}
		return nil, err
	}

	return s.riskDao.GetByTaskID(taskID)
}

// ListRisks 分页查询风险列表（支持多条件筛选）
func (s *riskService) ListRisks(page, pageSize int, taskID uint, riskLevel model.RiskLevel, indicatorType string) ([]*model.Risk, int64, error) {
	// 参数校验
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	return s.riskDao.List(page, pageSize, taskID, riskLevel, indicatorType)
}

// UpdateRisk 更新风险信息
func (s *riskService) UpdateRisk(id uint, riskLevel model.RiskLevel, indicatorType, indicatorName string) error {
	// 参数校验
	if id == 0 {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "风险ID不能为空")
	}
	if riskLevel == "" {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "风险类别不能为空")
	}
	if indicatorType == "" {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "指标大类不能为空")
	}
	if indicatorName == "" {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "指标名称不能为空")
	}

	// 验证风险类别是否有效
	if riskLevel != model.RiskLevelHigh && riskLevel != model.RiskLevelMedium && riskLevel != model.RiskLevelLow {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "无效的风险类别")
	}

	// 检查风险是否存在
	risk, err := s.riskDao.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return utils.NewAppError(utils.ErrNotFoundCode, "风险不存在")
		}
		return err
	}

	// 更新风险信息
	risk.RiskLevel = riskLevel
	risk.IndicatorType = indicatorType
	risk.IndicatorName = indicatorName

	return s.riskDao.Update(risk)
}

// DeleteRisk 删除风险
func (s *riskService) DeleteRisk(id uint) error {
	if id == 0 {
		return utils.NewAppError(utils.ErrInvalidParamsCode, "风险ID不能为空")
	}

	// 检查风险是否存在
	_, err := s.riskDao.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return utils.NewAppError(utils.ErrNotFoundCode, "风险不存在")
		}
		return err
	}

	return s.riskDao.Delete(id)
}
