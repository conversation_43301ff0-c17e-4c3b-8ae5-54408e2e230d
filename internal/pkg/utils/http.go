package utils

import (
	"fmt"
	"resty.dev/v3"
)

// Post   发送POST请求
func Post(url string, body interface{}, headers map[string]string) ([]byte, error) {
	client := resty.New()

	request := client.R().SetBody(body)

	// 设置请求头
	for key, value := range headers {
		request.SetHeader(key, value)
	}

	res, err := request.Post(url)
	if err != nil {
		return nil, fmt.Errorf("POST请求失败: %w", err)
	}

	return res.Bytes(), nil
}
