{"openapi": "3.0.3", "info": {"title": "风险管理API", "description": "风险管理相关的RESTful API接口文档，遵循RESTful设计规范", "version": "1.0.0", "contact": {"name": "API Support", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:8080/api", "description": "开发环境"}], "paths": {"/risks": {"get": {"tags": ["风险管理"], "summary": "获取风险列表", "description": "分页获取风险列表，支持任务ID、风险等级、指标大类筛选", "operationId": "listRisks", "parameters": [{"name": "page", "in": "query", "required": false, "description": "页码，默认1", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "page_size", "in": "query", "required": false, "description": "每页数量，默认10", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, {"name": "task_id", "in": "query", "required": false, "description": "任务ID筛选", "schema": {"type": "integer", "format": "int64", "minimum": 1}}, {"name": "risk_level", "in": "query", "required": false, "description": "风险等级筛选", "schema": {"type": "string", "enum": ["high", "medium", "low"]}}, {"name": "indicator_type", "in": "query", "required": false, "description": "指标大类模糊搜索", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/ListRisksResponse"}}}]}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "post": {"tags": ["风险管理"], "summary": "创建风险", "description": "创建新的风险记录", "operationId": "createRisk", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRiskRequest"}}}}, "responses": {"200": {"description": "创建成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/CreateRiskResponse"}}}]}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "3001": {"description": "任务不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/risks/{id}": {"get": {"tags": ["风险管理"], "summary": "获取风险详情", "description": "根据风险ID获取风险详细信息", "operationId": "getRiskByID", "parameters": [{"name": "id", "in": "path", "required": true, "description": "风险ID", "schema": {"type": "integer", "format": "int64", "minimum": 1}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/GetRiskResponse"}}}]}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "3002": {"description": "风险不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["风险管理"], "summary": "更新风险", "description": "更新指定风险的信息", "operationId": "updateRisk", "parameters": [{"name": "id", "in": "path", "required": true, "description": "风险ID", "schema": {"type": "integer", "format": "int64", "minimum": 1}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRiskRequest"}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "3002": {"description": "风险不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["风险管理"], "summary": "删除风险", "description": "删除指定的风险记录", "operationId": "deleteRisk", "parameters": [{"name": "id", "in": "path", "required": true, "description": "风险ID", "schema": {"type": "integer", "format": "int64", "minimum": 1}}], "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "3002": {"description": "风险不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/risks/task/{task_id}": {"get": {"tags": ["风险管理"], "summary": "根据任务ID获取风险列表", "description": "获取指定任务的所有风险记录", "operationId": "getRisksByTaskID", "parameters": [{"name": "task_id", "in": "path", "required": true, "description": "任务ID", "schema": {"type": "integer", "format": "int64", "minimum": 1}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/GetRisksByTaskIDResponse"}}}]}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "3001": {"description": "任务不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}}, "components": {"schemas": {"CreateRiskRequest": {"type": "object", "required": ["task_id", "risk_level", "indicator_type", "indicator_name"], "properties": {"task_id": {"type": "integer", "format": "int64", "description": "任务ID", "minimum": 1}, "risk_level": {"type": "string", "description": "风险等级", "enum": ["high", "medium", "low"]}, "indicator_type": {"type": "string", "description": "指标大类", "minLength": 1}, "indicator_name": {"type": "string", "description": "指标名称", "minLength": 1}}}, "UpdateRiskRequest": {"type": "object", "required": ["risk_level", "indicator_type", "indicator_name"], "properties": {"risk_level": {"type": "string", "description": "风险等级", "enum": ["high", "medium", "low"]}, "indicator_type": {"type": "string", "description": "指标大类", "minLength": 1}, "indicator_name": {"type": "string", "description": "指标名称", "minLength": 1}}}, "Risk": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "风险ID"}, "task_id": {"type": "integer", "format": "int64", "description": "任务ID"}, "risk_level": {"type": "string", "description": "风险等级", "enum": ["high", "medium", "low"]}, "indicator_type": {"type": "string", "description": "指标大类"}, "indicator_name": {"type": "string", "description": "指标名称"}, "created_at": {"type": "integer", "format": "int64", "description": "创建时间（毫秒时间戳）"}, "updated_at": {"type": "integer", "format": "int64", "description": "更新时间（毫秒时间戳）"}}}, "CreateRiskResponse": {"type": "object", "properties": {"risk": {"$ref": "#/components/schemas/Risk"}}}, "GetRiskResponse": {"type": "object", "properties": {"risk": {"$ref": "#/components/schemas/Risk"}}}, "GetRisksByTaskIDResponse": {"type": "object", "properties": {"risks": {"type": "array", "description": "风险列表", "items": {"$ref": "#/components/schemas/Risk"}}}}, "ListRisksResponse": {"type": "object", "properties": {"risks": {"type": "array", "description": "风险列表", "items": {"$ref": "#/components/schemas/Risk"}}, "total": {"type": "integer", "format": "int64", "description": "总数"}}}, "SuccessResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码，0表示成功", "example": 0}}}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误码"}, "message": {"type": "string", "description": "错误信息"}}}}}, "tags": [{"name": "风险管理", "description": "风险相关的管理操作"}]}