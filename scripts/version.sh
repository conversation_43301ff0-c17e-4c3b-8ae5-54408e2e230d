#!/bin/bash

# 版本管理脚本
# 用法: 
#   ./scripts/version.sh get     - 获取当前版本号
#   ./scripts/version.sh next    - 获取下一个版本号并更新文件
#   ./scripts/version.sh set N   - 设置版本号为N

VERSION_FILE="VERSION"

# 确保版本文件存在
if [ ! -f "$VERSION_FILE" ]; then
    echo "1" > "$VERSION_FILE"
fi

case "$1" in
    "get")
        # 获取当前版本号
        cat "$VERSION_FILE"
        ;;
    "next")
        # 获取当前版本号并递增
        current=$(cat "$VERSION_FILE")
        next=$((current + 1))
        echo "$next" > "$VERSION_FILE"
        echo "$next"
        ;;
    "set")
        # 设置指定版本号
        if [ -z "$2" ]; then
            echo "错误: 请提供版本号"
            echo "用法: $0 set <版本号>"
            exit 1
        fi
        echo "$2" > "$VERSION_FILE"
        echo "版本号已设置为: $2"
        ;;
    *)
        echo "用法: $0 {get|next|set <版本号>}"
        echo "  get  - 获取当前版本号"
        echo "  next - 获取下一个版本号并更新"
        echo "  set  - 设置指定版本号"
        exit 1
        ;;
esac
